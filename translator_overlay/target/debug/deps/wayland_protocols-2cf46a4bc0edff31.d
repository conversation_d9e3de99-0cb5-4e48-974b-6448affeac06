/home/<USER>/.config/eww/translator_overlay/target/debug/deps/wayland_protocols-2cf46a4bc0edff31.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/staging.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-activation-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/fullscreen-shell-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/idle-inhibit-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-method-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-timestamps-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/keyboard-shortcuts-inhibit-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/linux-dmabuf-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/linux-explicit-synchronization-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/pointer-constraints-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/pointer-gestures-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/primary-selection-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/relative-pointer-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/tablet-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/tablet-v2_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/text-input-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/text-input-v3_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-decoration-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-foreign-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-foreign-v2_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-output-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell-v5_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell-v6_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xwayland-keyboard-grab-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/gtk-primary-selection_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-method-unstable-v2_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/server-decoration_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-data-control-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-export-dmabuf-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-foreign-toplevel-management-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-gamma-control-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-input-inhibitor-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-layer-shell-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-output-management-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-output-power-management-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-screencopy-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-virtual-pointer-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/presentation-time_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/viewporter_client_api.rs

/home/<USER>/.config/eww/translator_overlay/target/debug/deps/libwayland_protocols-2cf46a4bc0edff31.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/staging.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-activation-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/fullscreen-shell-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/idle-inhibit-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-method-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-timestamps-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/keyboard-shortcuts-inhibit-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/linux-dmabuf-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/linux-explicit-synchronization-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/pointer-constraints-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/pointer-gestures-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/primary-selection-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/relative-pointer-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/tablet-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/tablet-v2_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/text-input-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/text-input-v3_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-decoration-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-foreign-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-foreign-v2_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-output-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell-v5_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell-v6_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xwayland-keyboard-grab-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/gtk-primary-selection_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-method-unstable-v2_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/server-decoration_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-data-control-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-export-dmabuf-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-foreign-toplevel-management-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-gamma-control-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-input-inhibitor-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-layer-shell-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-output-management-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-output-power-management-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-screencopy-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-virtual-pointer-v1_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/presentation-time_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell_client_api.rs /home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/viewporter_client_api.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/staging.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-activation-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/fullscreen-shell-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/idle-inhibit-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-method-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-timestamps-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/keyboard-shortcuts-inhibit-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/linux-dmabuf-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/linux-explicit-synchronization-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/pointer-constraints-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/pointer-gestures-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/primary-selection-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/relative-pointer-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/tablet-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/tablet-v2_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/text-input-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/text-input-v3_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-decoration-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-foreign-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-foreign-v2_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-output-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell-v5_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell-v6_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xwayland-keyboard-grab-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/gtk-primary-selection_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/input-method-unstable-v2_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/server-decoration_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-data-control-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-export-dmabuf-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-foreign-toplevel-management-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-gamma-control-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-input-inhibitor-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-layer-shell-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-output-management-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-output-power-management-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-screencopy-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/wlr-virtual-pointer-v1_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/presentation-time_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/xdg-shell_client_api.rs:
/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out/viewporter_client_api.rs:

# env-dep:OUT_DIR=/home/<USER>/.config/eww/translator_overlay/target/debug/build/wayland-protocols-f929618fae81452b/out
