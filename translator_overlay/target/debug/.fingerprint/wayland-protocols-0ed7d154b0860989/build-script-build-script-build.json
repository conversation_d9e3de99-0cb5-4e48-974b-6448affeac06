{"rustc": 9664862305575999237, "features": "[\"client\", \"staging_protocols\", \"unstable_protocols\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"staging_protocols\", \"unstable_protocols\", \"wayland-client\", \"wayland-server\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 14595776426079693095, "deps": [[13211618713646843096, "wayland_scanner", false, 2551657815737300530]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-0ed7d154b0860989/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}