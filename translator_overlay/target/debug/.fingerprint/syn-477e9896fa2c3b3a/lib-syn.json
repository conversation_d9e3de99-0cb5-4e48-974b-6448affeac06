{"rustc": 9664862305575999237, "features": "[\"clone-impls\", \"default\", \"derive\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 5716864620971403815, "deps": [[1988483478007900009, "unicode_ident", false, 359531848391205340], [3060637413840920116, "proc_macro2", false, 1008271295589370836], [17990358020177143287, "quote", false, 784713037895372262]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-477e9896fa2c3b3a/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}