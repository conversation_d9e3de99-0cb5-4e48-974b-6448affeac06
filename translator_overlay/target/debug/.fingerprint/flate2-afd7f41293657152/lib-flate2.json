{"rustc": 9664862305575999237, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2241668132362809309, "path": 11031146614792714702, "deps": [[7312356825837975969, "crc32fast", false, 14617006427515158962], [7636735136738807108, "miniz_oxide", false, 8687328463066388790]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-afd7f41293657152/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}