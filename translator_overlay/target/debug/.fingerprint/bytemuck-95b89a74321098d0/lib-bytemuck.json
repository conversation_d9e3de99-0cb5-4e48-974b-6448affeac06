{"rustc": 9664862305575999237, "features": "[\"aarch64_simd\", \"bytemuck_derive\", \"derive\"]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"alloc_uninit\", \"avx512_simd\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"impl_core_error\", \"latest_stable_rust\", \"min_const_generics\", \"must_cast\", \"must_cast_extra\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"pod_saturating\", \"track_caller\", \"transparentwrapper_extra\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\", \"zeroable_unwind_fn\"]", "target": 5195934831136530909, "profile": 639140734147086, "path": 15597623449855559385, "deps": [[7642298685304714569, "bytemuck_derive", false, 13773491912418889551]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bytemuck-95b89a74321098d0/dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}