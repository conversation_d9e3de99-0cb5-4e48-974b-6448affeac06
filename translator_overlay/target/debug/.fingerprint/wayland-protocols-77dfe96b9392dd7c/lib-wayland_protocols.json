{"rustc": 9664862305575999237, "features": "[\"client\", \"unstable\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"staging\", \"unstable\", \"wayland-client\", \"wayland-server\"]", "target": 17851254929718682527, "profile": 2241668132362809309, "path": 1499369238308989320, "deps": [[2050845139237290561, "wayland_scanner", false, 13698564675241351453], [7809932735238845763, "wayland_client", false, 6328992013114512259], [8255739460901517552, "wayland_backend", false, 7672055944182405141], [10435729446543529114, "bitflags", false, 4175435604284790813]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-77dfe96b9392dd7c/dep-lib-wayland_protocols", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}