{"rustc": 9664862305575999237, "features": "[\"event\", \"fs\", \"memoffset\", \"process\", \"signal\", \"socket\", \"time\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 16881288657864989906, "profile": 2241668132362809309, "path": 9886299048471752067, "deps": [[2828590642173593838, "cfg_if", false, 15045049975964435259], [4684437522915235464, "libc", false, 11834076341274749367], [10435729446543529114, "bitflags", false, 4175435604284790813], [15853578691500354095, "memoffset", false, 16435230010358882823]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-fc66e16ac2a400df/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}