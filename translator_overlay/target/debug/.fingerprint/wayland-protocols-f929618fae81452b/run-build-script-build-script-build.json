{"rustc": 9664862305575999237, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2838496963530108853, "build_script_build", false, 14407744199969122088]], "local": [{"RerunIfChanged": {"output": "debug/build/wayland-protocols-f929618fae81452b/output", "paths": ["./protocols/stable/presentation-time/presentation-time.xml", "./protocols/stable/viewporter/viewporter.xml", "./protocols/stable/xdg-shell/xdg-shell.xml", "./protocols/staging/xdg-activation/xdg-activation-v1.xml", "./misc/gtk-primary-selection.xml", "./misc/input-method-unstable-v2.xml", "./misc/server-decoration.xml", "./protocols/unstable/fullscreen-shell/fullscreen-shell-unstable-v1.xml", "./protocols/unstable/idle-inhibit/idle-inhibit-unstable-v1.xml", "./protocols/unstable/input-method/input-method-unstable-v1.xml", "./protocols/unstable/input-timestamps/input-timestamps-unstable-v1.xml", "./protocols/unstable/keyboard-shortcuts-inhibit/keyboard-shortcuts-inhibit-unstable-v1.xml", "./protocols/unstable/linux-dmabuf/linux-dmabuf-unstable-v1.xml", "./protocols/unstable/linux-explicit-synchronization/linux-explicit-synchronization-unstable-v1.xml", "./protocols/unstable/pointer-constraints/pointer-constraints-unstable-v1.xml", "./protocols/unstable/pointer-gestures/pointer-gestures-unstable-v1.xml", "./protocols/unstable/primary-selection/primary-selection-unstable-v1.xml", "./protocols/unstable/relative-pointer/relative-pointer-unstable-v1.xml", "./protocols/unstable/tablet/tablet-unstable-v1.xml", "./protocols/unstable/tablet/tablet-unstable-v2.xml", "./protocols/unstable/text-input/text-input-unstable-v1.xml", "./protocols/unstable/text-input/text-input-unstable-v3.xml", "./protocols/unstable/xdg-decoration/xdg-decoration-unstable-v1.xml", "./protocols/unstable/xdg-foreign/xdg-foreign-unstable-v1.xml", "./protocols/unstable/xdg-foreign/xdg-foreign-unstable-v2.xml", "./protocols/unstable/xdg-output/xdg-output-unstable-v1.xml", "./protocols/unstable/xdg-shell/xdg-shell-unstable-v5.xml", "./protocols/unstable/xdg-shell/xdg-shell-unstable-v6.xml", "./protocols/unstable/xwayland-keyboard-grab/xwayland-keyboard-grab-unstable-v1.xml", "./wlr-protocols/unstable/wlr-data-control-unstable-v1.xml", "./wlr-protocols/unstable/wlr-export-dmabuf-unstable-v1.xml", "./wlr-protocols/unstable/wlr-foreign-toplevel-management-unstable-v1.xml", "./wlr-protocols/unstable/wlr-gamma-control-unstable-v1.xml", "./wlr-protocols/unstable/wlr-input-inhibitor-unstable-v1.xml", "./wlr-protocols/unstable/wlr-layer-shell-unstable-v1.xml", "./wlr-protocols/unstable/wlr-output-management-unstable-v1.xml", "./wlr-protocols/unstable/wlr-output-power-management-unstable-v1.xml", "./wlr-protocols/unstable/wlr-screencopy-unstable-v1.xml", "./wlr-protocols/unstable/wlr-virtual-pointer-unstable-v1.xml"]}}], "rustflags": [], "config": 0, "compile_kind": 0}