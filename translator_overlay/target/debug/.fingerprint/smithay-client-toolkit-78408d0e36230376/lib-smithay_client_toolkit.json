{"rustc": 9664862305575999237, "features": "[\"calloop\", \"default\", \"dlopen\"]", "declared_features": "[\"calloop\", \"default\", \"dlopen\"]", "target": 7000747139203390044, "profile": 2241668132362809309, "path": 10135981225192844446, "deps": [[1410059298442353473, "build_script_build", false, 6556905551933728502], [2838496963530108853, "wayland_protocols", false, 6877467801751359587], [5986029879202738730, "log", false, 4825504122500992498], [6803188373723125608, "dlib", false, 17346673016123853811], [8243684182090116245, "wayland_cursor", false, 5134455391835881094], [9011620124493104570, "wayland_client", false, 243345958460615106], [9223765023884963089, "nix", false, 1830379015180516834], [9799420377467640938, "calloop", false, 13362666628808459766], [10435729446543529114, "bitflags", false, 4175435604284790813], [10504454274054532777, "memmap2", false, 1509395098665928314], [17917672826516349275, "lazy_static", false, 5842093854929372019]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/smithay-client-toolkit-78408d0e36230376/dep-lib-smithay_client_toolkit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}