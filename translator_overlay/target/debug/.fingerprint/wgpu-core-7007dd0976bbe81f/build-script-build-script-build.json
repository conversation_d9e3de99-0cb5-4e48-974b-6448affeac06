{"rustc": 9664862305575999237, "features": "[\"default\", \"raw-window-handle\"]", "declared_features": "[\"angle\", \"default\", \"id32\", \"raw-window-handle\", \"replay\", \"ron\", \"serde\", \"serial-pass\", \"strict_asserts\", \"trace\", \"vulkan-portability\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 7963510725491845021, "deps": [[13650835054453599687, "cfg_aliases", false, 5629737721432769984]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wgpu-core-7007dd0976bbe81f/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}