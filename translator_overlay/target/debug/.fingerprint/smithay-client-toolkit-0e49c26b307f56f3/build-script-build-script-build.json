{"rustc": 9664862305575999237, "features": "[\"calloop\", \"default\", \"pkg-config\", \"xkbcommon\"]", "declared_features": "[\"calloop\", \"default\", \"pkg-config\", \"xkbcommon\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 14233708143402750287, "deps": [[3214373357989284387, "pkg_config", false, 18374267525198790917]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/smithay-client-toolkit-0e49c26b307f56f3/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}