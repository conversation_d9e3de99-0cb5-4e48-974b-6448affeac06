{"rustc": 9664862305575999237, "features": "[\"clone\", \"codespan-reporting\", \"default\", \"glsl-out\", \"hexf-parse\", \"span\", \"spirv\", \"spv-out\", \"termcolor\", \"unicode-xid\", \"validate\", \"wgsl-in\"]", "declared_features": "[\"arbitrary\", \"clone\", \"codespan-reporting\", \"default\", \"deserialize\", \"dot-out\", \"glsl-in\", \"glsl-out\", \"hexf-parse\", \"hlsl-out\", \"msl-out\", \"petgraph\", \"pp-rs\", \"serde\", \"serialize\", \"span\", \"spirv\", \"spv-in\", \"spv-out\", \"termcolor\", \"unicode-xid\", \"validate\", \"wgsl-in\", \"wgsl-out\"]", "target": 4086359053712496688, "profile": 2241668132362809309, "path": 611542010068768233, "deps": [[4206236867992986649, "bit_set", false, 7483730754561795897], [5157631553186200874, "num_traits", false, 13958834041472951152], [5986029879202738730, "log", false, 4825504122500992498], [6511967590362104379, "codespan_reporting", false, 3469830531176175775], [8008191657135824715, "thiserror", false, 14058068525436736833], [10435729446543529114, "bitflags", false, 4175435604284790813], [11064059710780544854, "spirv", false, 11758011467823211005], [11741667666137467643, "hexf_parse", false, 3715374626905529169], [12902659978838094914, "termcolor", false, 5119020073598773949], [14923790796823607459, "indexmap", false, 15576908538805199387], [16055916053474393816, "rustc_hash", false, 3253735955116613276], [16126285161989458480, "unicode_xid", false, 3282695532912559902]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/naga-237efe0490a43095/dep-lib-naga", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}