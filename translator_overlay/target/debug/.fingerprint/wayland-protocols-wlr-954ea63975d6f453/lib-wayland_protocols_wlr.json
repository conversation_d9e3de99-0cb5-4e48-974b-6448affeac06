{"rustc": 9664862305575999237, "features": "[\"client\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"wayland-client\", \"wayland-server\"]", "target": 16532630540568316278, "profile": 2241668132362809309, "path": 5778086331509655719, "deps": [[2050845139237290561, "wayland_scanner", false, 13698564675241351453], [4123832577239087307, "wayland_protocols", false, 15716150277506795758], [7809932735238845763, "wayland_client", false, 6328992013114512259], [8255739460901517552, "wayland_backend", false, 7672055944182405141], [10435729446543529114, "bitflags", false, 4175435604284790813]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-wlr-954ea63975d6f453/dep-lib-wayland_protocols_wlr", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}