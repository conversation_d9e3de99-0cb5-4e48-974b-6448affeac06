{"rustc": 9664862305575999237, "features": "[\"calloop\", \"default\", \"pkg-config\", \"xkbcommon\"]", "declared_features": "[\"calloop\", \"default\", \"pkg-config\", \"xkbcommon\"]", "target": 16218806119597165848, "profile": 2241668132362809309, "path": 3871130409350520986, "deps": [[2050845139237290561, "wayland_scanner", false, 13698564675241351453], [3569804531943532339, "wayland_cursor", false, 794150167961479107], [4123832577239087307, "wayland_protocols", false, 15716150277506795758], [5145168002166378358, "nix", false, 17450115574072259803], [5674016251873108882, "xkbcommon", false, 13163757771542570290], [5986029879202738730, "log", false, 4825504122500992498], [6221634788973696348, "wayland_protocols_wlr", false, 8294633320648085929], [6803188373723125608, "dlib", false, 17346673016123853811], [7809932735238845763, "wayland_client", false, 6328992013114512259], [8008191657135824715, "thiserror", false, 14058068525436736833], [8255739460901517552, "wayland_backend", false, 7672055944182405141], [9799420377467640938, "calloop", false, 13362666628808459766], [10435729446543529114, "bitflags", false, 4175435604284790813], [10504454274054532777, "memmap2", false, 1509395098665928314], [12837107350224122326, "build_script_build", false, 6220087706572547592], [17917672826516349275, "lazy_static", false, 5842093854929372019]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/smithay-client-toolkit-4381d2e1f715f457/dep-lib-smithay_client_toolkit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}