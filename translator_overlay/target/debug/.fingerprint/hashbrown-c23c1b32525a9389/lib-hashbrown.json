{"rustc": 9664862305575999237, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 2354346919074255712, "deps": [[966925859616469517, "ahash", false, 4905813777008023778], [9150530836556604396, "allocator_api2", false, 10886805813729278308]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-c23c1b32525a9389/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}