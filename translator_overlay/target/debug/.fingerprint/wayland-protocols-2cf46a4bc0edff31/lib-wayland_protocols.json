{"rustc": 9664862305575999237, "features": "[\"client\", \"staging_protocols\", \"unstable_protocols\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"staging_protocols\", \"unstable_protocols\", \"wayland-client\", \"wayland-server\"]", "target": 17851254929718682527, "profile": 2241668132362809309, "path": 4186811881649911841, "deps": [[2838496963530108853, "build_script_build", false, 16824189234698332629], [9011620124493104570, "wayland_client", false, 243345958460615106], [10435729446543529114, "bitflags", false, 4175435604284790813], [14682610449618970944, "wayland_commons", false, 14889049286100068407]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-2cf46a4bc0edff31/dep-lib-wayland_protocols", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}