{"rustc": 9664862305575999237, "features": "[\"dlopen\", \"scoped-tls\", \"use_system_lib\"]", "declared_features": "[\"dlopen\", \"scoped-tls\", \"use_system_lib\"]", "target": 6885752962907571681, "profile": 2241668132362809309, "path": 14798097258917552802, "deps": [[4684437522915235464, "libc", false, 11834076341274749367], [9011620124493104570, "build_script_build", false, 16228994442462713838], [9223765023884963089, "nix", false, 1830379015180516834], [10435729446543529114, "bitflags", false, 4175435604284790813], [11434239582363224126, "downcast_rs", false, 938039598838847911], [12570133544693084162, "wayland_sys", false, 11416997902209468674], [13370890382188185363, "scoped_tls", false, 17546088740840323554], [14682610449618970944, "wayland_commons", false, 14889049286100068407]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-client-29eee283515defae/dep-lib-wayland_client", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}