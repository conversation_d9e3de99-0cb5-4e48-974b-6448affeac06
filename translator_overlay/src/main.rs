use anyhow::Result;
use winit::{event_loop::EventLoop, window::WindowBuilder};
use wgpu_glyph::{ab_glyph, GlyphBrushBuilder};

fn main() -> Result<()> {
    // Inicializa event loop y crea ventana
    let event_loop = EventLoop::new();
    let window = WindowBuilder::new()
        .with_title("Translator Overlay")
        .with_inner_size(winit::dpi::LogicalSize::new(600.0, 200.0))
        .with_transparent(true)
        .with_decorations(false)
        .build(&event_loop)?;

    // Inicializa WGPU
    let instance = wgpu::Instance::default();
    let surface = unsafe { instance.create_surface(&window) }?;
    let adapter = pollster::block_on(instance.request_adapter(&wgpu::RequestAdapterOptions {
        power_preference: wgpu::PowerPreference::HighPerformance,
        compatible_surface: Some(&surface),
        force_fallback_adapter: false,
    }))
    .unwrap();

    let (device, queue) = pollster::block_on(adapter.request_device(
        &wgpu::DeviceDescriptor {
            features: wgpu::Features::empty(),
            limits: wgpu::Limits::default(),
            label: None,
        },
        None,
    ))?;

    let surface_caps = surface.get_capabilities(&adapter);
    let surface_format = surface_caps.formats[0];

    let size = window.inner_size();
    let config = wgpu::SurfaceConfiguration {
        usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
        format: surface_format,
        width: size.width,
        height: size.height,
        present_mode: wgpu::PresentMode::Fifo,
        alpha_mode: surface_caps.alpha_modes[0],
    };
    surface.configure(&device, &config);

    // Fuente de sistema o fallback embebido
    let font_data = include_bytes!("/usr/share/fonts/TTF/DejaVuSans.ttf");
    let font = ab_glyph::FontArc::try_from_slice(font_data)?;
    let _glyph_brush = GlyphBrushBuilder::using_font(font).build(&device, surface_format);

    println!("Ventana inicializada correctamente.");
    Ok(())
}