use anyhow::Result;
use winit::{
    event::{Event, WindowEvent},
    event_loop::{ControlFlow, EventLoop},
    window::WindowBuilder,
};
use wgpu_glyph::{ab_glyph, GlyphBrushBuilder, Section, Text};

// Shader para efecto glassmorphism
const GLASSMORPHISM_SHADER: &str = r#"
struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) uv: vec2<f32>,
}

@vertex
fn vs_main(@builtin(vertex_index) vertex_index: u32) -> VertexOutput {
    var out: VertexOutput;
    let x = f32(i32(vertex_index) - 1);
    let y = f32(i32(vertex_index & 1u) * 2 - 1);
    out.clip_position = vec4<f32>(x, y, 0.0, 1.0);
    out.uv = vec2<f32>((x + 1.0) * 0.5, (1.0 - y) * 0.5);
    return out;
}

@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
    let center = vec2<f32>(0.5, 0.5);
    let dist = distance(in.uv, center);

    // Gradiente radial para efecto glassmorphism
    let gradient = 1.0 - smoothstep(0.0, 0.7, dist);

    // Color base con efecto cristal
    let base_color = vec3<f32>(0.15, 0.15, 0.2);
    let highlight = vec3<f32>(0.3, 0.3, 0.4);

    let final_color = mix(base_color, highlight, gradient * 0.3);

    // Alpha con efecto de transparencia glassmorphism
    let alpha = 0.15 + gradient * 0.1;

    return vec4<f32>(final_color, alpha);
}
"#;

fn main() -> Result<()> {
    // Inicializa event loop y crea ventana
    let event_loop = EventLoop::new();
    let window = WindowBuilder::new()
        .with_title("Translator Overlay")
        .with_inner_size(winit::dpi::LogicalSize::new(600.0, 200.0))
        .with_transparent(true)
        .with_decorations(false)
        .with_resizable(false)
        .build(&event_loop)?;

    // Inicializa WGPU
    let instance = wgpu::Instance::new(wgpu::Backends::all());
    let surface = unsafe { instance.create_surface(&window) };
    let adapter = pollster::block_on(instance.request_adapter(&wgpu::RequestAdapterOptions {
        power_preference: wgpu::PowerPreference::HighPerformance,
        compatible_surface: Some(&surface),
        force_fallback_adapter: false,
    }))
    .unwrap();

    let (device, queue) = pollster::block_on(adapter.request_device(
        &wgpu::DeviceDescriptor {
            features: wgpu::Features::empty(),
            limits: wgpu::Limits::default(),
            label: None,
        },
        None,
    ))?;

    // Crear staging belt para renderizado de texto
    let mut staging_belt = wgpu::util::StagingBelt::new(1024);

    let surface_formats = surface.get_supported_formats(&adapter);
    let surface_format = surface_formats[0];
    let alpha_modes = surface.get_supported_alpha_modes(&adapter);

    let size = window.inner_size();
    let config = wgpu::SurfaceConfiguration {
        usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
        format: surface_format,
        width: size.width,
        height: size.height,
        present_mode: wgpu::PresentMode::Fifo,
        alpha_mode: alpha_modes[0],
    };
    surface.configure(&device, &config);

    // Crear shader para efecto glassmorphism
    let shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
        label: Some("Glassmorphism Shader"),
        source: wgpu::ShaderSource::Wgsl(GLASSMORPHISM_SHADER.into()),
    });

    // Crear render pipeline para glassmorphism
    let render_pipeline = device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
        label: Some("Glassmorphism Pipeline"),
        layout: Some(&device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("Glassmorphism Pipeline Layout"),
            bind_group_layouts: &[],
            push_constant_ranges: &[],
        })),
        vertex: wgpu::VertexState {
            module: &shader,
            entry_point: "vs_main",
            buffers: &[],
        },
        fragment: Some(wgpu::FragmentState {
            module: &shader,
            entry_point: "fs_main",
            targets: &[Some(wgpu::ColorTargetState {
                format: surface_format,
                blend: Some(wgpu::BlendState {
                    color: wgpu::BlendComponent {
                        src_factor: wgpu::BlendFactor::SrcAlpha,
                        dst_factor: wgpu::BlendFactor::OneMinusSrcAlpha,
                        operation: wgpu::BlendOperation::Add,
                    },
                    alpha: wgpu::BlendComponent {
                        src_factor: wgpu::BlendFactor::One,
                        dst_factor: wgpu::BlendFactor::OneMinusSrcAlpha,
                        operation: wgpu::BlendOperation::Add,
                    },
                }),
                write_mask: wgpu::ColorWrites::ALL,
            })],
        }),
        primitive: wgpu::PrimitiveState {
            topology: wgpu::PrimitiveTopology::TriangleList,
            strip_index_format: None,
            front_face: wgpu::FrontFace::Ccw,
            cull_mode: None,
            unclipped_depth: false,
            polygon_mode: wgpu::PolygonMode::Fill,
            conservative: false,
        },
        depth_stencil: None,
        multisample: wgpu::MultisampleState {
            count: 1,
            mask: !0,
            alpha_to_coverage_enabled: false,
        },
        multiview: None,
    });

    // Fuente de sistema o fallback embebido
    let font_data = include_bytes!("/usr/share/fonts/TTF/DejaVuSans.ttf");
    let font = ab_glyph::FontArc::try_from_slice(font_data)?;
    let mut glyph_brush = GlyphBrushBuilder::using_font(font).build(&device, surface_format);

    println!("Ventana inicializada correctamente.");

    // Ejecutar el event loop
    event_loop.run(move |event, _, control_flow| {
        *control_flow = ControlFlow::Wait;

        match event {
            Event::WindowEvent {
                ref event,
                window_id,
            } if window_id == window.id() => match event {
                WindowEvent::CloseRequested => *control_flow = ControlFlow::Exit,
                WindowEvent::Resized(physical_size) => {
                    // Reconfigurar la superficie cuando cambie el tamaño
                    let config = wgpu::SurfaceConfiguration {
                        usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
                        format: surface_format,
                        width: physical_size.width,
                        height: physical_size.height,
                        present_mode: wgpu::PresentMode::Fifo,
                        alpha_mode: alpha_modes[0],
                    };
                    surface.configure(&device, &config);
                }
                _ => {}
            },
            Event::RedrawRequested(window_id) if window_id == window.id() => {
                // Aquí iría el código de renderizado
                // Por ahora solo limpiamos la pantalla
                match surface.get_current_texture() {
                    Ok(output) => {
                        let view = output.texture.create_view(&wgpu::TextureViewDescriptor::default());
                        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
                            label: Some("Render Encoder"),
                        });

                        {
                            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                                label: Some("Glassmorphism Render Pass"),
                                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                                    view: &view,
                                    resolve_target: None,
                                    ops: wgpu::Operations {
                                        load: wgpu::LoadOp::Clear(wgpu::Color {
                                            r: 0.0,
                                            g: 0.0,
                                            b: 0.0,
                                            a: 0.0,  // Completamente transparente para que el shader maneje la transparencia
                                        }),
                                        store: true,
                                    },
                                })],
                                depth_stencil_attachment: None,
                            });

                            // Usar nuestro pipeline de glassmorphism
                            render_pass.set_pipeline(&render_pipeline);
                            render_pass.draw(0..3, 0..1); // Triángulo que cubre toda la pantalla
                        }

                        // Agregar texto "Hola Mundo" centrado
                        let window_size = window.inner_size();
                        glyph_brush.queue(Section {
                            screen_position: (window_size.width as f32 / 2.0, window_size.height as f32 / 2.0),
                            bounds: (window_size.width as f32, window_size.height as f32),
                            text: vec![Text::new("Hola Mundo")
                                .with_color([1.0, 1.0, 1.0, 0.9]) // Blanco con alta opacidad
                                .with_scale(48.0)], // Tamaño de fuente grande
                            layout: wgpu_glyph::Layout::default()
                                .h_align(wgpu_glyph::HorizontalAlign::Center)
                                .v_align(wgpu_glyph::VerticalAlign::Center),
                        });

                        // Renderizar el texto
                        glyph_brush.draw_queued(
                            &device,
                            &mut staging_belt,
                            &mut encoder,
                            &view,
                            window_size.width,
                            window_size.height,
                        ).expect("Error al renderizar texto");

                        // Finalizar staging belt
                        staging_belt.finish();

                        queue.submit(std::iter::once(encoder.finish()));
                        output.present();
                    }
                    Err(wgpu::SurfaceError::Lost) => {
                        // Reconfigurar la superficie si se pierde
                        let size = window.inner_size();
                        let config = wgpu::SurfaceConfiguration {
                            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
                            format: surface_format,
                            width: size.width,
                            height: size.height,
                            present_mode: wgpu::PresentMode::Fifo,
                            alpha_mode: alpha_modes[0],
                        };
                        surface.configure(&device, &config);
                    }
                    Err(wgpu::SurfaceError::OutOfMemory) => *control_flow = ControlFlow::Exit,
                    Err(e) => eprintln!("{:?}", e),
                }
            }
            Event::MainEventsCleared => {
                // Solicitar redibujado
                window.request_redraw();
            }
            _ => {}
        }
    })
}