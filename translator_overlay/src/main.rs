use anyhow::Result;
use winit::{
    event::{Event, WindowEvent},
    event_loop::{ControlFlow, EventLoop},
    window::WindowBuilder,
};
use wgpu_glyph::{ab_glyph, GlyphBrushBuilder};

fn main() -> Result<()> {
    // Inicializa event loop y crea ventana
    let event_loop = EventLoop::new();
    let window = WindowBuilder::new()
        .with_title("Translator Overlay")
        .with_inner_size(winit::dpi::LogicalSize::new(600.0, 200.0))
        .with_transparent(true)
        .with_decorations(false)
        .build(&event_loop)?;

    // Inicializa WGPU
    let instance = wgpu::Instance::new(wgpu::Backends::all());
    let surface = unsafe { instance.create_surface(&window) };
    let adapter = pollster::block_on(instance.request_adapter(&wgpu::RequestAdapterOptions {
        power_preference: wgpu::PowerPreference::HighPerformance,
        compatible_surface: Some(&surface),
        force_fallback_adapter: false,
    }))
    .unwrap();

    let (device, queue) = pollster::block_on(adapter.request_device(
        &wgpu::DeviceDescriptor {
            features: wgpu::Features::empty(),
            limits: wgpu::Limits::default(),
            label: None,
        },
        None,
    ))?;

    let surface_formats = surface.get_supported_formats(&adapter);
    let surface_format = surface_formats[0];
    let alpha_modes = surface.get_supported_alpha_modes(&adapter);

    let size = window.inner_size();
    let config = wgpu::SurfaceConfiguration {
        usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
        format: surface_format,
        width: size.width,
        height: size.height,
        present_mode: wgpu::PresentMode::Fifo,
        alpha_mode: alpha_modes[0],
    };
    surface.configure(&device, &config);

    // Fuente de sistema o fallback embebido
    let font_data = include_bytes!("/usr/share/fonts/TTF/DejaVuSans.ttf");
    let font = ab_glyph::FontArc::try_from_slice(font_data)?;
    let _glyph_brush = GlyphBrushBuilder::using_font(font).build(&device, surface_format);

    println!("Ventana inicializada correctamente.");

    // Ejecutar el event loop
    event_loop.run(move |event, _, control_flow| {
        *control_flow = ControlFlow::Wait;

        match event {
            Event::WindowEvent {
                ref event,
                window_id,
            } if window_id == window.id() => match event {
                WindowEvent::CloseRequested => *control_flow = ControlFlow::Exit,
                WindowEvent::Resized(physical_size) => {
                    // Reconfigurar la superficie cuando cambie el tamaño
                    let config = wgpu::SurfaceConfiguration {
                        usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
                        format: surface_format,
                        width: physical_size.width,
                        height: physical_size.height,
                        present_mode: wgpu::PresentMode::Fifo,
                        alpha_mode: alpha_modes[0],
                    };
                    surface.configure(&device, &config);
                }
                _ => {}
            },
            Event::RedrawRequested(window_id) if window_id == window.id() => {
                // Aquí iría el código de renderizado
                // Por ahora solo limpiamos la pantalla
                match surface.get_current_texture() {
                    Ok(output) => {
                        let view = output.texture.create_view(&wgpu::TextureViewDescriptor::default());
                        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
                            label: Some("Render Encoder"),
                        });

                        {
                            let _render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                                label: Some("Render Pass"),
                                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                                    view: &view,
                                    resolve_target: None,
                                    ops: wgpu::Operations {
                                        load: wgpu::LoadOp::Clear(wgpu::Color {
                                            r: 0.0,
                                            g: 0.0,
                                            b: 0.0,
                                            a: 0.5, // Semi-transparente
                                        }),
                                        store: true,
                                    },
                                })],
                                depth_stencil_attachment: None,
                            });
                        }

                        queue.submit(std::iter::once(encoder.finish()));
                        output.present();
                    }
                    Err(wgpu::SurfaceError::Lost) => {
                        // Reconfigurar la superficie si se pierde
                        let size = window.inner_size();
                        let config = wgpu::SurfaceConfiguration {
                            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
                            format: surface_format,
                            width: size.width,
                            height: size.height,
                            present_mode: wgpu::PresentMode::Fifo,
                            alpha_mode: alpha_modes[0],
                        };
                        surface.configure(&device, &config);
                    }
                    Err(wgpu::SurfaceError::OutOfMemory) => *control_flow = ControlFlow::Exit,
                    Err(e) => eprintln!("{:?}", e),
                }
            }
            Event::MainEventsCleared => {
                // Solicitar redibujado
                window.request_redraw();
            }
            _ => {}
        }
    })
}